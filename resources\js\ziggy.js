const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"products.index":{"uri":"products","methods":["GET","HEAD"]},"products.show":{"uri":"products\/{product}","methods":["GET","HEAD"],"parameters":["product"],"bindings":{"product":"id"}},"products.store":{"uri":"products","methods":["POST"]},"cart.add":{"uri":"cart\/add","methods":["POST"]},"cart.index":{"uri":"cart","methods":["GET","HEAD"]},"cart.update":{"uri":"cart\/{product}\/update","methods":["POST"],"parameters":["product"],"bindings":{"product":"id"}},"cart.remove":{"uri":"cart\/{product}","methods":["DELETE"],"parameters":["product"],"bindings":{"product":"id"}},"cart.clear":{"uri":"cart\/clear","methods":["POST"]},"cart.checkout":{"uri":"cart\/checkout","methods":["POST"]},"recipes.index":{"uri":"recipes","methods":["GET","HEAD"]},"recipes.show":{"uri":"recipes\/{recipe}","methods":["GET","HEAD"],"parameters":["recipe"],"bindings":{"recipe":"id"}},"recipes.store":{"uri":"recipes","methods":["POST"]},"recipes.reviews.store":{"uri":"recipes\/{recipe}\/reviews","methods":["POST"],"parameters":["recipe"],"bindings":{"recipe":"id"}},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.products.index":{"uri":"admin\/products","methods":["GET","HEAD"]},"admin.products.create":{"uri":"admin\/products\/create","methods":["GET","HEAD"]},"admin.products.store":{"uri":"admin\/products","methods":["POST"]},"admin.products.edit":{"uri":"admin\/products\/{product}\/edit","methods":["GET","HEAD"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.update":{"uri":"admin\/products\/{product}","methods":["PUT"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.destroy":{"uri":"admin\/products\/{product}","methods":["DELETE"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.stock.update":{"uri":"admin\/products\/{product}\/stock","methods":["PUT"],"parameters":["product"],"bindings":{"product":"id"}},"admin.products.stock.batch-update":{"uri":"admin\/products\/batch-stock-update","methods":["POST"]},"admin.orders.index":{"uri":"admin\/orders","methods":["GET","HEAD"]},"admin.orders.show":{"uri":"admin\/orders\/{order}","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"admin.orders.status.update":{"uri":"admin\/orders\/{order}\/status","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"admin.recipes.index":{"uri":"admin\/recipes","methods":["GET","HEAD"]},"admin.recipes.create":{"uri":"admin\/recipes\/create","methods":["GET","HEAD"]},"admin.recipes.store":{"uri":"admin\/recipes","methods":["POST"]},"admin.recipes.edit":{"uri":"admin\/recipes\/{recipe}\/edit","methods":["GET","HEAD"],"parameters":["recipe"],"bindings":{"recipe":"id"}},"admin.recipes.update":{"uri":"admin\/recipes\/{recipe}","methods":["PUT"],"parameters":["recipe"],"bindings":{"recipe":"id"}},"admin.recipes.destroy":{"uri":"admin\/recipes\/{recipe}","methods":["DELETE"],"parameters":["recipe"],"bindings":{"recipe":"id"}},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
